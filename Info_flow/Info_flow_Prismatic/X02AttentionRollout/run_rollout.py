#!/usr/bin/env python3
import argparse
import os
import math
import re
from typing import List, Tuple, Optional

import torch
import numpy as np
from PIL import Image


def add_prismatic_to_path():
    prismatic_repo = "/home/<USER>/X/Info_flow/Info_flow_Prismatic/prismatic-vlms"
    if prismatic_repo not in os.sys.path:
        os.sys.path.insert(0, prismatic_repo)


add_prismatic_to_path()

from prismatic.models import load as prismatic_load  # type: ignore


# -------------------- 通用工具 --------------------
def make_safe_dirname(text: str, max_len: int = 100) -> str:
    t = text.strip().replace('<image>', 'img').replace(' ', '_')
    t = re.sub(r'[<>:"/\\|?*.,;!@#$%^&()=+`~\[\]{}]', '', t)
    t = re.sub(r'_+', '_', t).strip('_')
    return t[:max_len]


def get_model_basename(model_path: str) -> str:
    return os.path.basename(model_path.rstrip('/'))


def load_vlm(local_model_dir: str, device: torch.device):
    vlm = prismatic_load(local_model_dir)
    # 为避免 7B 在 24GB 显存上 OOM，先在 CPU 上降精度，再整体迁移到目标设备
    # 降精度：仅对 LLM 使用 half，视觉与投影保持 FP32，后续在构造融合嵌入时显式转换
    try:
        vlm.llm_backbone.llm.half()
    except Exception:
        pass
    vlm.to(device)
    vlm.eval()
    return vlm


def build_pixel_values(vlm, image_path: str, device: torch.device):
    image = Image.open(image_path).convert("RGB")
    image_transform = vlm.vision_backbone.get_image_transform()
    pixel_values = image_transform(image)
    # 若视觉主干为 half，则将像素张量也转换为 half 以避免 dtype 不匹配
    try:
        vb_param = next(vlm.vision_backbone.parameters())
        vision_is_half = vb_param.dtype == torch.float16
    except StopIteration:
        vision_is_half = False
    if vision_is_half:
        if isinstance(pixel_values, torch.Tensor):
            pixel_values = pixel_values.half()
        elif isinstance(pixel_values, dict):
            pixel_values = {k: v.half() for k, v in pixel_values.items()}
    if isinstance(pixel_values, torch.Tensor):
        pixel_values = pixel_values[None, ...].to(device)
    elif isinstance(pixel_values, dict):
        pixel_values = {k: v[None, ...].to(device) for k, v in pixel_values.items()}
    else:
        raise ValueError(f"Unsupported pixel_values type: {type(pixel_values)}")
    return image, pixel_values


def decode_tokens(vlm, input_ids: torch.Tensor) -> List[str]:
    tok = vlm.llm_backbone.tokenizer
    return tok.convert_ids_to_tokens(input_ids[0].tolist(), skip_special_tokens=False)


def get_text_ids(vlm, text: str, device: torch.device) -> torch.Tensor:
    tok = vlm.llm_backbone.tokenizer
    return tok(text, truncation=True, return_tensors="pt").input_ids.to(device)


def forward_multimodal(
    vlm,
    input_ids: torch.Tensor,
    pixel_values,
    labels: Optional[torch.Tensor] = None,
    output_attentions: bool = False,
    use_cache: Optional[bool] = None,
    past_key_values=None,
    return_dict: bool = True,
):
    # 构造融合嵌入（BOS + VIS + TEXT[1:])，并直接调用底层 LLMBackbone
    # 假设 batch=1
    assert input_ids.shape[0] == 1, "仅支持 batch=1"
    # 1) 视觉特征 -> 投影到 LLM 维度
    if isinstance(pixel_values, dict):
        patch_features = vlm.vision_backbone({k: pixel_values[k] for k in pixel_values})
    else:
        patch_features = vlm.vision_backbone(pixel_values)
    proj = vlm.projector(patch_features)
    # 2) 文本嵌入
    txt = vlm.llm_backbone.embed_input_ids(input_ids)
    # 3) dtype 对齐到 LLM 参数 dtype
    dtype_llm = next(vlm.llm_backbone.llm.parameters()).dtype
    if proj.dtype != dtype_llm:
        proj = proj.to(dtype=dtype_llm)
    if txt.dtype != dtype_llm:
        txt = txt.to(dtype=dtype_llm)
    # 4) 拼接融合嵌入
    fused = torch.cat([txt[:, :1, :], proj, txt[:, 1:, :]], dim=1)
    # 5) 直接走底层 LLM 的 forward
    return vlm.llm_backbone(
        input_ids=None,
        attention_mask=None,
        position_ids=None,
        past_key_values=past_key_values,
        inputs_embeds=fused,
        labels=labels,
        use_cache=use_cache,
        output_attentions=output_attentions,
        output_hidden_states=False,
        return_dict=return_dict,
    )


def get_fused_layout(att_layer: torch.Tensor, text_len: int) -> Tuple[int, int, int]:
    # att_layer: [1, H, S, S]
    S = int(att_layer.shape[-1])
    num_vis = S - text_len
    if num_vis <= 0:
        raise RuntimeError(f"视觉 token 数量非正: num_vis={num_vis}, S={S}, text_len={text_len}")
    return S, num_vis, text_len


def infer_grid(num_vis_tokens: int) -> Tuple[int, int, int]:
    side = int(math.sqrt(num_vis_tokens))
    if side * side == num_vis_tokens:
        return side, side, num_vis_tokens
    grid_tokens = num_vis_tokens - 1
    side = int(math.sqrt(grid_tokens))
    if side * side == grid_tokens:
        return side, side, grid_tokens
    for s in (14, 16):
        if s * s == num_vis_tokens:
            return s, s, num_vis_tokens
        if s * s == grid_tokens:
            return s, s, grid_tokens
    raise RuntimeError(f"无法推断方形网格: num_vis={num_vis_tokens}")


def normalize_local(x: np.ndarray) -> np.ndarray:
    mn, mx = float(x.min()), float(x.max())
    if mx > mn:
        return (x - mn) / (mx - mn)
    return np.zeros_like(x)


def overlay_and_save(base_img: Image.Image, grid: np.ndarray, title: str, out_path: str, alpha: float = 0.6):
    import matplotlib.pyplot as plt
    W, H = base_img.size
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    plt.figure(figsize=(5, 5))
    plt.imshow(base_img)
    plt.imshow(grid, cmap='viridis', alpha=alpha, interpolation='nearest', origin='upper', extent=(0, W, H, 0))
    plt.axis('off')
    plt.title(title)
    plt.savefig(out_path, bbox_inches='tight', dpi=150)
    plt.close()


# -------------------- Rollout / Flow 核心 --------------------
def prepare_layer_attention(
    att_layer: torch.Tensor,
    add_residual: bool = True,
    residual_weight: Optional[float] = None,
    norm: str = 'row',  # 'row' or 'none'
) -> torch.Tensor:
    # att_layer: [1, H, S, S] -> 平均头 -> [S, S]；可选 +I 与规范化
    A = att_layer[0].mean(dim=0)  # [S, S]
    S1, S2 = A.shape
    if S1 != S2:
        raise RuntimeError(f"注意力非方阵: {A.shape}")
    I = torch.eye(S1, device=A.device, dtype=A.dtype)
    if residual_weight is not None:
        # 线性混合： (1-w)·I + w·A
        w = float(residual_weight)
        w = 0.0 if w < 0 else (1.0 if w > 1.0 else w)
        A = (1.0 - w) * I + w * A
    elif add_residual:
        A = A + I
    if norm == 'row':
        row_sum = A.sum(dim=-1, keepdim=True).clamp_min(1e-12)
        A = A / row_sum
    elif norm == 'none':
        pass
    else:
        raise ValueError(f"Unknown norm mode: {norm}")
    return A


def attention_rollout(
    attentions: List[torch.Tensor],
    residual_weight: Optional[float] = None,
    norm: str = 'row',
    last_k: int = 0,
) -> List[torch.Tensor]:
    # 返回逐层累计的矩阵列表：M_i = A_i @ ... @ A_0 （可选 last_k）
    mats_all = [
        prepare_layer_attention(a, residual_weight=residual_weight, norm=norm)
        for a in attentions
    ]
    mats = mats_all[-last_k:] if (isinstance(last_k, int) and last_k > 0 and last_k < len(mats_all)) else mats_all
    cum = []
    for i, A in enumerate(mats):
        if i == 0:
            cum.append(A)
        else:
            cum.append(A @ cum[i-1])
    return cum


def attention_flow_maxprod(
    attentions: List[torch.Tensor],
    j_count: int,
    device: torch.device,
    vis_start_idx: int,
) -> List[torch.Tensor]:
    """
    基于最大乘积路径（等价最小负对数和）的 attention flow。

    关键修正：初始化时应当把每个视觉 token 的“起点”置于融合序列中视觉区段的真实索引，
    而不是从 0 开始。融合布局为 [BOS][VIS...][TEXT...]，因此可视化的 j 维（视觉 token 计数）
    需要与在融合序列中的起始位置对齐，即 s = vis_start_idx + j。
    之前的实现把 V[j, j] = 0 错误地锚定到了序列开头（包含 BOS），导致所有路径都从索引 0
    出发，出现“只亮左上角 patch”的退化现象。

    参数：
      - j_count:      参与 Flow 计算的视觉 token 个数（通常等于网格 patch 数=Gh*Gw）。
      - vis_start_idx:融合序列中第一个被可视化的视觉 token 的索引（BOS 之后，若存在 CLS 则为 2）。
    返回：
      - 每层的 DP 结果张量列表 V_i，形状皆为 [S, j_count]。
    """

    mats = [prepare_layer_attention(a) for a in attentions]
    costs = [(-torch.log(m.clamp_min(1e-12))).to(device) for m in mats]
    S = int(costs[0].shape[0])

    # V[i, j]: 到达序列位置 i，且源头为第 j 个“可视化视觉 token”的最小代价
    V = torch.full((S, j_count), float('inf'), device=device)
    for j in range(j_count):
        s = vis_start_idx + j  # 视觉 token 在融合序列中的真实位置
        if 0 <= s < S:
            V[s, j] = 0.0

    Vs = []
    for k in range(len(costs)):
        Ck = costs[k]  # [i, s]
        # 选择从上一位置 s 迁移到 i 的最小代价路径
        T = Ck.unsqueeze(2) + V.unsqueeze(0)  # [i, s, j]
        V = T.min(dim=1).values  # [i, j]
        Vs.append(V.clone())
    return Vs


# -------------------- 主流程 --------------------
def main():
    parser = argparse.ArgumentParser(description='Prismatic 注意力逐层传播 / Flow（与 Paligemma 输出格式一致）')
    parser.add_argument('--model_dir', type=str, required=True, help='本地 prismatic 模型目录（含 config.json 与 checkpoints）')
    parser.add_argument('--image', type=str, required=True, help='图像路径')
    parser.add_argument('--prompt', type=str, required=True, help='文本提示')
    parser.add_argument('--out_dir', type=str, required=True, help='输出目录（将创建子目录）')
    parser.add_argument('--method', type=str, choices=['rollout', 'flow-maxprod', 'both'], default='both')
    # Rollout variants (to avoid flat maps on deep stacks)
    parser.add_argument('--rollout_norm', type=str, choices=['row', 'none'], default='row')
    parser.add_argument('--rollout_residual', type=float, default=1.0, help='w in (1-w)I + wA; set <1 to sharpen')
    parser.add_argument('--rollout_last_k', type=int, default=0, help='use only last-k layers for rollout; 0=all')
    parser.add_argument('--max_new_tokens', type=int, default=12)
    parser.add_argument('--device', type=str, default='cuda' if torch.cuda.is_available() else 'cpu')
    args = parser.parse_args()

    device = torch.device(args.device)

    print('加载模型与构建输入...')
    vlm = load_vlm(args.model_dir, device)
    base_img_pil, pixel_values = build_pixel_values(vlm, args.image, device)
    # 调试 dtype（一次性打印）
    try:
        if isinstance(pixel_values, dict):
            print(f"[dbg] pixel dtypes: ", {k: v.dtype for k, v in pixel_values.items()})
        else:
            print(f"[dbg] pixel dtype: {pixel_values.dtype}")
    except Exception:
        pass
    tokenizer = vlm.llm_backbone.tokenizer

    # 生成文本（用于区分 prompt/output token）
    print('进行文本生成（获取输出 token 段）...')
    # 自定义贪心生成（避免 transformers GenerationMixin 的兼容性问题）
    def greedy_generate(vlm, image_pil: Image.Image, prompt_text: str, max_new_tokens: int) -> str:
        tok = vlm.llm_backbone.tokenizer
        input_ids = tok(prompt_text, truncation=True, return_tensors="pt").input_ids.to(device)
        # 构造像素输入
        img_transform = vlm.vision_backbone.get_image_transform()
        pv = img_transform(image_pil)
        if isinstance(pv, torch.Tensor):
            pv = pv[None, ...].to(device)
        elif isinstance(pv, dict):
            pv = {k: v[None, ...].to(device) for k, v in pv.items()}
        else:
            raise ValueError(f"Unsupported pixel_values type: {type(pv)}")

        with torch.no_grad():
            out = forward_multimodal(
                vlm,
                input_ids=input_ids,
                pixel_values=pv,
                labels=None,
                use_cache=True,
                return_dict=True,
            )
        gen_token_ids: List[int] = []
        eos_id = tok.eos_token_id
        past = out.past_key_values
        last_logits = out.logits[:, -1, :]
        next_id = int(torch.argmax(last_logits, dim=-1).item())
        if eos_id is not None and next_id == eos_id:
            return ""
        gen_token_ids.append(next_id)
        for _ in range(max_new_tokens - 1):
            next_input = torch.tensor([[next_id]], dtype=torch.long, device=device)
            with torch.no_grad():
                out = vlm(
                    input_ids=next_input,
                    past_key_values=past,
                    use_cache=True,
                    return_dict=True,
                )
            past = out.past_key_values
            last_logits = out.logits[:, -1, :]
            next_id = int(torch.argmax(last_logits, dim=-1).item())
            if eos_id is not None and next_id == eos_id:
                break
            gen_token_ids.append(next_id)

        if len(gen_token_ids) == 0:
            return ""
        gen_ids = torch.tensor(gen_token_ids, dtype=torch.long)[None, :]
        return tok.decode(gen_ids[0], skip_special_tokens=True).strip()

    gen_text = greedy_generate(vlm, base_img_pil, args.prompt, args.max_new_tokens)
    combined_text = args.prompt + gen_text

    # 获取 prompt / combined 的文本 ID
    input_ids_prompt = get_text_ids(vlm, args.prompt, device)
    input_ids_combined = get_text_ids(vlm, combined_text, device)

    # 提取注意力（对 combined 文本）
    print('前向推理并提取注意力...')
    with torch.no_grad():
        out = forward_multimodal(
            vlm,
            input_ids=input_ids_combined,
            pixel_values=pixel_values,
            labels=None,
            output_attentions=True,
            return_dict=True,
        )
    attentions = out.attentions
    if not isinstance(attentions, (list, tuple)) or len(attentions) == 0:
        raise RuntimeError('模型未返回 attentions（请确保 output_attentions=True）')

    # 解析融合序列结构
    S, num_vis, text_len = get_fused_layout(attentions[0], text_len=int(input_ids_combined.shape[1]))
    grid_h, grid_w, maybe_trim = infer_grid(num_vis)
    trim = (maybe_trim != num_vis)
    eff_vis = maybe_trim

    # 文本 token（用于标签与切分）
    tokens_prompt = tokenizer.convert_ids_to_tokens(input_ids_prompt[0].tolist(), skip_special_tokens=False)
    tokens_combined = tokenizer.convert_ids_to_tokens(input_ids_combined[0].tolist(), skip_special_tokens=False)

    # 映射到融合序列索引：融合序列 = [BOS] + [VIS * num_vis] + [TEXT(不含BOS)]
    fused_first_text_idx = 1 + num_vis
    n_prompt_text_ex_bos = max(0, int(input_ids_prompt.shape[1]) - 1)
    n_comb_text_ex_bos = max(0, int(input_ids_combined.shape[1]) - 1)
    output_start_idx = fused_first_text_idx + n_prompt_text_ex_bos
    output_end_idx = fused_first_text_idx + n_comb_text_ex_bos

    # 基图像尺寸
    try:
        _, Ht, Wt = vlm.vision_backbone.default_image_resolution
        base_img_resized = base_img_pil.resize((Wt, Ht))
    except Exception:
        base_img_resized = base_img_pil.resize((224, 224))

    # 输出路径结构（与 Paligemma 一致）
    model_base = get_model_basename(args.model_dir)
    safe_prompt = make_safe_dirname(args.prompt)
    main_out = os.path.join(args.out_dir, f'{model_base}_{safe_prompt}')

    methods = ['rollout', 'flow-maxprod'] if args.method == 'both' else [args.method]
    for method in methods:
        if method == 'rollout':
            cum = attention_rollout(
                attentions,
                residual_weight=args.rollout_residual,
                norm=args.rollout_norm,
                last_k=args.rollout_last_k,
            )
            vis_offset = (num_vis - eff_vis)
            vis_start_idx = 1 + vis_offset
            # Debug: quick stats to detect uniform maps
            try:
                probe_q = fused_first_text_idx + max(0, n_prompt_text_ex_bos - 1)
                probe_slice = cum[-1][probe_q, vis_start_idx:vis_start_idx + eff_vis].detach().float().cpu().numpy()
                print(f"[dbg][rollout] slice stats min={probe_slice.min():.4e} max={probe_slice.max():.4e} std={probe_slice.std():.4e}")
            except Exception:
                pass
        else:
            # 视觉区段在融合序列中的起始索引：BOS(0) + 可能的视觉前导 token（如 CLS）
            vis_offset = (num_vis - eff_vis)  # 0 或 1（若存在一个非网格视觉 token）
            vis_start_idx = 1 + vis_offset
            V_layers = attention_flow_maxprod(
                attentions,
                j_count=eff_vis,
                device=device,
                vis_start_idx=vis_start_idx,
            )

        base_view = os.path.join(main_out, 'rollout_views' if method == 'rollout' else 'flow_views')
        view_prompt = os.path.join(base_view, 'prompt')
        view_output = os.path.join(base_view, 'output')
        os.makedirs(view_prompt, exist_ok=True)
        os.makedirs(view_output, exist_ok=True)

        print(f'渲染并保存热力图（{method}，Prompt 组）...')
        prompt_maps = []
        prompt_tokens_ex_bos = tokens_prompt[1:] if len(tokens_prompt) > 0 else []
        for t_idx, tok in enumerate(prompt_tokens_ex_bos):
            fused_idx = fused_first_text_idx + t_idx
            if method == 'rollout':
                # 忽略可能存在的视觉 CLS：从真实的视觉起点 vis_start_idx 开始切片
                contrib = cum[-1][fused_idx, vis_start_idx:vis_start_idx + eff_vis].detach().float().cpu().numpy()
            else:
                costs = V_layers[-1][fused_idx, :].detach().float().cpu().numpy()
                contrib = np.exp(-costs)
            # 已经在切片中对齐了视觉起点；此处无需再次截断
            try:
                grid = contrib.reshape(grid_h, grid_w)
            except Exception:
                continue
            prompt_maps.append((t_idx, tok, grid))
            safe_tok = re.sub(r'[^\w]+', '_', tok)[:40]
            out_name = f"{method}_token_{t_idx}_{safe_tok}_nearest.png"
            out_path = os.path.join(view_prompt, out_name)
            overlay_and_save(base_img_resized, normalize_local(grid), f"{method} (prompt) token[{t_idx}] '{tok}'", out_path)

        if prompt_maps:
            agg = np.mean([m[2] for m in prompt_maps], axis=0)
            out_path = os.path.join(view_prompt, f'{method}_agg_mean_over_prompt_tokens_nearest.png')
            overlay_and_save(base_img_resized, normalize_local(agg), f'{method}: mean over prompt tokens', out_path)

        print(f'渲染并保存热力图（{method}，Output 组）...')
        output_maps = []
        output_count = max(0, output_end_idx - output_start_idx)
        for rel in range(output_count):
            fused_idx = output_start_idx + rel
            text_pos_ex_bos = n_prompt_text_ex_bos + rel
            tok = tokens_combined[1 + text_pos_ex_bos] if 1 + text_pos_ex_bos < len(tokens_combined) else f"tok{rel}"
            if method == 'rollout':
                contrib = cum[-1][fused_idx, vis_start_idx:vis_start_idx + eff_vis].detach().float().cpu().numpy()
            else:
                costs = V_layers[-1][fused_idx, :].detach().float().cpu().numpy()
                contrib = np.exp(-costs)
            # rollout 已用 vis_start_idx 对齐，flow 的 j 维本身就是 eff_vis
            try:
                grid = contrib.reshape(grid_h, grid_w)
            except Exception:
                continue
            output_maps.append((rel, tok, grid))
            safe_tok = re.sub(r'[^\w]+', '_', tok)[:40]
            out_name = f"{method}_out_token_{rel}_{safe_tok}_nearest.png"
            out_path = os.path.join(view_output, out_name)
            overlay_and_save(base_img_resized, normalize_local(grid), f"{method} (output) token[{rel}] '{tok}'", out_path)

        if output_maps:
            agg = np.mean([m[2] for m in output_maps], axis=0)
            out_path = os.path.join(view_output, f'{method}_agg_mean_over_output_tokens_nearest.png')
            overlay_and_save(base_img_resized, normalize_local(agg), f'{method}: mean over output tokens', out_path)

        print(f"保存完成：{base_view}")


if __name__ == '__main__':
    main()
