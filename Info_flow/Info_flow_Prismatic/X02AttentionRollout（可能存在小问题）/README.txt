Prismatic X02AttentionRollout (Attention Rollout / Flow)
========================================================

功能
- 在 Prismatic VLM 上实现注意力逐层传播（Attention Rollout）与基于最大乘积路径的 Attention Flow（flow-maxprod）。
- 结果目录/文件命名与 Paligemma 版本保持一致（除了 HTML 之外）。

依赖
- Conda 环境：spatialvla
- 项目代码：/home/<USER>/X/Info_flow/Info_flow_Prismatic/prismatic-vlms
- 模型：/home/<USER>/X/models/prism-dinosiglip-224px+7b/prism-dinosiglip-224px+7b

运行示例

  source /home/<USER>/software/anaconda3/bin/activate spatialvla
  python /home/<USER>/X/Info_flow/Info_flow_Prismatic/X02AttentionRollout/run_rollout.py \
    --model_dir /home/<USER>/X/models/prism-dinosiglip-224px+7b/prism-dinosiglip-224px+7b \
    --image /home/<USER>/X/coke.png \
    --prompt "pick up the coke can" \
    --out_dir /home/<USER>/X/Info_flow/Info_flow_Prismatic/X02AttentionRollout/Result \
    --method both \
    --max_new_tokens 12 \
    --device cuda:4

说明
- 输出将写入 out_dir 下的子目录：{model_base}_{safe_prompt}/(rollout_views|flow_views)/(prompt|output)
- 每个 token 会生成一张 PNG 热力图，另有分组聚合（mean）图。
- 交互 HTML 可选；本实现默认只写静态 PNG（与 Paligemma 要求一致）。
