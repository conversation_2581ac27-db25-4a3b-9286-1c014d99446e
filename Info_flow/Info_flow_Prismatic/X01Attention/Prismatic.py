#!/usr/bin/env python3
import os
import math
import argparse
from typing import List, Tu<PERSON>, Optional, Dict

import torch
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt


def add_prismatic_to_path():
    prismatic_repo = "/home/<USER>/X/Info_flow/Info_flow_Prismatic/prismatic-vlms"
    if prismatic_repo not in os.sys.path:
        os.sys.path.insert(0, prismatic_repo)


add_prismatic_to_path()

from prismatic.models import load as prismatic_load  # type: ignore


def load_vlm(local_model_dir: str, device: torch.device) -> "PrismaticVLM":
    vlm = prismatic_load(local_model_dir)
    vlm.to(device)
    vlm.eval()
    return vlm


def build_inputs(vlm: "PrismaticVLM", image_path: str, text: str, device: torch.device):
    image = Image.open(image_path).convert("RGB")
    tokenizer = vlm.llm_backbone.tokenizer
    input_ids = tokenizer(text, truncation=True, return_tensors="pt").input_ids.to(device)

    image_transform = vlm.vision_backbone.get_image_transform()
    pixel_values = image_transform(image)
    if isinstance(pixel_values, torch.Tensor):
        pixel_values = pixel_values[None, ...].to(device)
    elif isinstance(pixel_values, dict):
        pixel_values = {k: v[None, ...].to(device) for k, v in pixel_values.items()}
    else:
        raise ValueError(f"Unsupported pixel_values type: {type(pixel_values)}")

    return image, input_ids, pixel_values


def get_attentions(
    vlm: "PrismaticVLM",
    input_ids: torch.Tensor,
    pixel_values,
    device: torch.device,
):
    with torch.no_grad():
        out = vlm(
            input_ids=input_ids,
            pixel_values=pixel_values,
            labels=None,
            output_attentions=True,
            return_dict=True,
        )
    # out.attentions: list[num_layers] of tensors [bsz, n_heads, seq, seq]
    return out.attentions


def decode_tokens(vlm: "PrismaticVLM", input_ids: torch.Tensor) -> List[str]:
    tok = vlm.llm_backbone.tokenizer
    # Use tokenizer fast method to get token strings
    tokens = tok.convert_ids_to_tokens(input_ids[0].tolist(), skip_special_tokens=False)
    return tokens


def get_fused_sequence_lengths(
    attentions_layer: torch.Tensor,  # [1, H, S, S]
    text_len: int,
) -> Tuple[int, int, int]:
    seq_len = attentions_layer.shape[-1]
    # fused = 1 (BOS) + num_vis + (text_len - 1)
    num_vis = seq_len - text_len
    # conservative guards
    if num_vis <= 0:
        raise RuntimeError(f"Computed non-positive visual token count: {num_vis}, seq_len={seq_len}, text_len={text_len}")
    return seq_len, num_vis, text_len


def map_vis_tokens_to_grid(num_vis_tokens: int) -> Tuple[int, int, int]:
    # Prefer the case with NO CLS (common in TIMM intermediate features)
    if num_vis_tokens <= 0:
        raise RuntimeError("Visual token count too small to form grid.")
    side = int(math.sqrt(num_vis_tokens))
    if side * side == num_vis_tokens:
        return side, side, num_vis_tokens
    # Fallback: assume 1 CLS present -> try num_vis_tokens-1
    grid_tokens = num_vis_tokens - 1
    side = int(math.sqrt(grid_tokens))
    if side * side == grid_tokens:
        return side, side, grid_tokens
    # Additional common sizes
    for s in (14, 16):
        if s * s == num_vis_tokens:
            return s, s, num_vis_tokens
        if s * s == grid_tokens:
            return s, s, grid_tokens
    raise RuntimeError(f"Visual grid not square-ish: num_vis={num_vis_tokens}")


def ensure_numpy(x: torch.Tensor) -> np.ndarray:
    return x.detach().float().cpu().numpy()


def overlay_grid_heatmap(
    base_img: Image.Image,
    grid_map: np.ndarray,  # shape [Gh, Gw] in patch grid
    title: str,
    out_path: str,
    alpha: float = 0.6,
):
    W, H = base_img.size
    plt.figure(figsize=(5, 5))
    plt.imshow(base_img)
    # 使用离散最近邻插值，让每个patch保持单一颜色；使用extent将网格铺满整张图像
    plt.imshow(
        grid_map,
        cmap="viridis",
        alpha=alpha,
        interpolation="nearest",
        origin="upper",
        extent=(0, W, H, 0),
    )
    plt.axis("off")
    plt.title(title)
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    plt.savefig(out_path, bbox_inches="tight", dpi=150)
    plt.close()


def save_grid_only(
    grid_map: np.ndarray,
    title: str,
    out_path: str,
):
    plt.figure(figsize=(5, 5))
    im = plt.imshow(grid_map, cmap="viridis", interpolation="nearest", origin="upper")
    plt.colorbar(im, fraction=0.046, pad=0.04)
    plt.axis("off")
    plt.title(title)
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    plt.savefig(out_path, bbox_inches="tight", dpi=150)
    plt.close()


def upsample_to_image_nearest(heat_grid: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
    # 最近邻：保持每个patch为同一颜色
    hm = Image.fromarray((heat_grid * 255).astype(np.uint8))
    hm = hm.resize(target_size, resample=Image.NEAREST)
    return np.array(hm).astype(np.float32) / 255.0


def normalize_map_local(x: np.ndarray) -> np.ndarray:
    xmin, xmax = float(x.min()), float(x.max())
    if xmax > xmin:
        return (x - xmin) / (xmax - xmin)
    return np.zeros_like(x)


def select_token_indices(tokens: List[str], mode: str, query: Optional[str]) -> List[int]:
    # mode: all | match | last
    if mode == "all":
        return list(range(len(tokens)))
    if mode == "last":
        return [len(tokens) - 1]
    if mode == "match":
        if query is None:
            return list(range(len(tokens)))
        q = query.lower()
        idxs = [i for i, t in enumerate(tokens) if q in t.lower()]
        return idxs if idxs else list(range(len(tokens)))
    return list(range(len(tokens)))


def main():
    parser = argparse.ArgumentParser(description="Prismatic VLM - Raw Attention Map Visualization (text token -> image patches)")
    parser.add_argument("--model_dir", type=str, required=True, help="Local prismatic run dir (contains config.json, checkpoints)")
    parser.add_argument("--image", type=str, required=True, help="Path to image file")
    parser.add_argument("--text", type=str, required=True, help="Input prompt text")
    parser.add_argument("--out_dir", type=str, required=True, help="Output directory for results")
    parser.add_argument("--layer", type=int, default=-1, help="Layer index to visualize (default: last layer)")
    parser.add_argument("--all_layers", action="store_true", help="If set, visualize all layers")
    parser.add_argument("--avg_heads", action="store_true", help="Average over heads instead of per-head")
    parser.add_argument("--token_mode", type=str, default="match", choices=["all", "match", "last"], help="Which tokens to visualize")
    parser.add_argument("--token_query", type=str, default="coke", help="Substring to match when token_mode=match")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu")
    args = parser.parse_args()

    device = torch.device(args.device)

    # Load model
    vlm = load_vlm(args.model_dir, device)

    # Prepare inputs
    base_img_pil, input_ids, pixel_values = build_inputs(vlm, args.image, args.text, device)

    # Get attentions
    attentions = get_attentions(vlm, input_ids, pixel_values, device)
    if not isinstance(attentions, (list, tuple)) or len(attentions) == 0:
        raise RuntimeError("No attentions returned by the model (set output_attentions=True).")

    # Utility inner function for a single layer
    def visualize_one_layer(layer_index: int, layer_out_dir: Optional[str] = None) -> Optional[np.ndarray]:
        attn = attentions[layer_index]  # [1, H, S, S]
        assert attn.dim() == 4 and attn.shape[0] == 1, f"Unexpected attention shape: {attn.shape}"

        # Compute fused sequence structure (identical across layers but cheap)
        _, num_vis_total, _ = get_fused_sequence_lengths(attn, text_len=input_ids.shape[1])
        grid_h, grid_w, grid_tokens = map_vis_tokens_to_grid(num_vis_total)
        vis_offset = num_vis_total - grid_tokens  # 0 if no extra token, 1 if visual CLS present
        vis_start_idx = 1 + vis_offset

        # Prepare per-layer directories
        per_layer_dir = layer_out_dir or args.out_dir
        per_token_dir = os.path.join(per_layer_dir, "per_token")
        os.makedirs(per_token_dir, exist_ok=True)

        # Attention format: [B=1, H, S_q, S_k]
        attn_np = ensure_numpy(attn)[0]  # [H, S, S]

        # For aggregated across selected tokens
        agg_map = None
        for ti in token_indices:
            fused_q = 1 + num_vis_total + (ti - 1) if ti > 0 else 0
            if fused_q < 0 or fused_q >= attn_np.shape[-2]:
                continue

            # Average heads for visualization (or keep mean as baseline)
            a_q = attn_np.mean(axis=0)[fused_q]

            # Slice only the patch tokens region (skip BOS and any visual CLS)
            vis_slice = a_q[vis_start_idx : vis_start_idx + grid_tokens]
            if vis_slice.shape[0] <= 0:
                continue
            vis_grid = vis_slice.reshape(grid_h, grid_w)

            vis_norm = normalize_map_local(vis_grid)

            token_label = tokens[ti] if 0 <= ti < len(tokens) else f"tok{ti}"
            safe_tok = token_label.replace('/', '_').replace(':', '_')
            title = f"Layer {layer_index} | Token: {token_label}"
            out_path = os.path.join(per_token_dir, f"layer{layer_index}_token{ti}_{safe_tok}.png")
            overlay_grid_heatmap(base_img_resized, vis_norm, title, out_path)
            # Grid-only
            grid_only_path = os.path.join(per_token_dir, f"layer{layer_index}_token{ti}_{safe_tok}_grid.png")
            save_grid_only(vis_norm, f"Grid Only | {title}", grid_only_path)

            # accumulate for per-layer aggregation (across tokens)
            agg_map = vis_norm if agg_map is None else (agg_map + vis_norm)

        if agg_map is None:
            return None
        agg_map = agg_map / max(1, len(token_indices))
        # Save aggregated (per-layer) maps
        out_path = os.path.join(per_layer_dir, f"layer{layer_index}_tokens_aggregate.png")
        overlay_grid_heatmap(
            base_img_resized, agg_map, f"Layer {layer_index} | Aggregated over tokens", out_path
        )
        grid_only_path = os.path.join(per_layer_dir, f"layer{layer_index}_tokens_aggregate_grid.png")
        save_grid_only(agg_map, f"Grid Only | Layer {layer_index} Aggregated", grid_only_path)
        return agg_map

    # Decode tokens for selection (once)
    tokens = decode_tokens(vlm, input_ids)
    token_indices = select_token_indices(tokens, args.token_mode, args.token_query)

    # Build base image at target size (224x224 is typical for this model id)
    try:
        _, H, W = vlm.vision_backbone.default_image_resolution
    except Exception:
        H, W = 224, 224
    base_img_resized = base_img_pil.resize((W, H))

    # Prepare output dirs
    os.makedirs(args.out_dir, exist_ok=True)

    if args.all_layers:
        # Per-layer outputs in subfolders and a layers-mosaic summary
        all_layer_aggs = []
        for li in range(len(attentions)):
            layer_dir = os.path.join(args.out_dir, f"layer_{li:02d}")
            os.makedirs(layer_dir, exist_ok=True)
            agg = visualize_one_layer(li, layer_out_dir=layer_dir)
            if agg is not None:
                all_layer_aggs.append((li, agg))

        # Build mosaic of per-layer aggregated maps (grid-only, for scanability)
        if len(all_layer_aggs) > 0:
            cols = 8  # 32 layers -> 4 rows x 8 cols
            rows = int(math.ceil(len(all_layer_aggs) / cols))
            plt.figure(figsize=(2.2 * cols, 2.2 * rows))
            for idx, (li, agg) in enumerate(all_layer_aggs):
                ax = plt.subplot(rows, cols, idx + 1)
                ax.imshow(agg, cmap="viridis", interpolation="nearest", origin="upper")
                ax.set_title(f"L{li}", fontsize=8)
                ax.axis("off")
            mosaic_path = os.path.join(args.out_dir, "layers_aggregate_grid_mosaic.png")
            plt.tight_layout()
            plt.savefig(mosaic_path, bbox_inches="tight", dpi=150)
            plt.close()

            # Also save across-layers average overlay
            avg_all = sum(agg for _, agg in all_layer_aggs) / len(all_layer_aggs)
            out_path = os.path.join(args.out_dir, "layers_aggregate_over_tokens_and_layers.png")
            overlay_grid_heatmap(
                base_img_resized,
                avg_all,
                "Aggregated across tokens and layers",
                out_path,
            )
            grid_only_path = os.path.join(args.out_dir, "layers_aggregate_over_tokens_and_layers_grid.png")
            save_grid_only(avg_all, "Grid Only | Aggregated over tokens and layers", grid_only_path)

    else:
        # Determine layer to use (single)
        layer_index = args.layer if args.layer >= 0 else (len(attentions) - 1)
        if layer_index < 0 or layer_index >= len(attentions):
            raise ValueError(f"Invalid layer index {layer_index}; model has {len(attentions)} layers")
        visualize_one_layer(layer_index, layer_out_dir=args.out_dir)

    print(f"Saved results to: {args.out_dir}")


if __name__ == "__main__":
    main()
