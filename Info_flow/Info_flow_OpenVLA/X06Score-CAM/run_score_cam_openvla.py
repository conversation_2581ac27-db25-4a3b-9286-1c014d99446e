#!/usr/bin/env python3
import argparse
import json
import math
import os
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
from transformers import AutoModelForVision2Seq, AutoProcessor


def ensure_dir(path: str) -> None:
    os.makedirs(path, exist_ok=True)


def to_numpy(x: torch.Tensor) -> np.ndarray:
    x = x.detach().float().cpu()
    return x.numpy()


def normalize_minmax(x: torch.Tensor, eps: float = 1e-8) -> torch.Tensor:
    xmin = x.amin(dim=(-2, -1), keepdim=True)
    xmax = x.amax(dim=(-2, -1), keepdim=True)
    denom = (xmax - xmin).clamp_min(eps)
    return (x - xmin) / denom


def square_side_or_minus_one(n: int) -> int:
    s = int(round(math.sqrt(n)))
    return s if s * s == n else -1


def auto_device_dtype() -> Tuple[torch.device, torch.dtype]:
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    if device.type == "cuda":
        if torch.cuda.is_bf16_supported():
            return device, torch.bfloat16
        return device, torch.float16
    return device, torch.float32


def overlay_heatmap(base_rgb: torch.Tensor, heatmap: torch.Tensor, alpha: float = 0.5) -> np.ndarray:
    import matplotlib.cm as cm

    h = heatmap.clamp(0, 1).detach().cpu().float().numpy()
    color = cm.get_cmap("viridis")(h)[..., :3]
    base = base_rgb.detach().cpu().float().numpy().transpose(1, 2, 0)
    base = base.clip(0, 1)
    out = (1 - alpha) * base + alpha * color
    return (out * 255).clip(0, 255).astype(np.uint8)


def get_openvla_prompt(instruction: str, model_path: str) -> str:
    # Mirror openvla/vla-scripts/deploy.py behavior
    if "v01" in model_path:
        system_prompt = (
            "A chat between a curious user and an artificial intelligence assistant. "
            "The assistant gives helpful, detailed, and polite answers to the user's questions."
        )
        return f"{system_prompt} USER: What action should the robot take to {instruction.lower()}? ASSISTANT:"
    else:
        return f"In: What action should the robot take to {instruction.lower()}?\nOut:"


def find_last_conv2d(model: torch.nn.Module) -> Optional[Tuple[str, torch.nn.Module]]:
    preferred: Optional[Tuple[str, torch.nn.Module]] = None
    fallback: Optional[Tuple[str, torch.nn.Module]] = None
    for name, m in model.named_modules():
        if isinstance(m, torch.nn.Conv2d):
            lname = name.lower()
            # Prefer within explicit vision backbone naming
            if "vision_backbone" in lname or "featurizer" in lname:
                preferred = (name, m)
            else:
                fallback = (name, m)
    return preferred or fallback


def resolve_target_layer(model: torch.nn.Module, explicit: Optional[str] = None) -> Tuple[str, torch.nn.Module]:
    if explicit:
        for name, m in model.named_modules():
            if name == explicit:
                return name, m
        raise ValueError(f"Target layer '{explicit}' not found in model modules.")

    found = find_last_conv2d(model)
    if found is not None:
        return found

    # Prefer last self-attention block within vision backbone
    last_attn: Optional[Tuple[str, torch.nn.Module]] = None
    for name, m in model.named_modules():
        lname = name.lower()
        if name.endswith("attn") and ("vision_backbone" in lname or "featurizer" in lname):
            last_attn = (name, m)
    if last_attn is None:
        for name, m in model.named_modules():
            if name.endswith("attn"):
                last_attn = (name, m)
    if last_attn is not None:
        return last_attn

    # Fallback: projector layers (outputs [B, N, D])
    for cand in ["projector.fc2", "projector.fc3", "projector.fc1", "projector"]:
        for name, m in model.named_modules():
            if name == cand:
                return name, m

    # Ultimately fallback to model itself (unlikely)
    return ("", model)


def score_next_token(
    outputs,
    processor: AutoProcessor,
    inputs: Dict[str, torch.Tensor],
    target_text: Optional[str] = None,
) -> torch.Tensor:
    if not hasattr(outputs, "logits"):
        raise RuntimeError("Model outputs do not contain logits for next-token scoring.")
    logits = outputs.logits  # [B, T, V]
    last_logits = logits[:, -1, :]
    if target_text:
        ids = processor.tokenizer(target_text, add_special_tokens=False).input_ids
        if len(ids) == 0:
            return last_logits.max(dim=-1).values.mean()
        vals = last_logits[:, ids].mean(dim=-1)
        return vals.mean()
    return last_logits.max(dim=-1).values.mean()


def score_teacher_forced(
    model,
    processor: AutoProcessor,
    prompt: str,
    image: Image.Image,
    base_inputs: Dict[str, torch.Tensor],
    target_text: str,
) -> torch.Tensor:
    if not target_text:
        raise ValueError("teacher_forced scoring requires --target_text.")

    ti = processor(text=f"{prompt} {target_text}", images=image, return_tensors="pt")
    ti = {k: (v.to(base_inputs["pixel_values"].device)) for k, v in ti.items()}
    # Align dtypes
    for k in list(ti.keys()):
        if isinstance(ti[k], torch.Tensor) and ti[k].is_floating_point():
            ti[k] = ti[k].to(base_inputs["pixel_values"].dtype)
    ti["pixel_values"] = base_inputs["pixel_values"]
    if "intrinsic" in base_inputs:
        ti["intrinsic"] = base_inputs["intrinsic"]

    with torch.no_grad():
        out = model(**ti, use_cache=False)

    if not hasattr(out, "logits"):
        return torch.tensor(0.0, device=base_inputs["pixel_values"].device, dtype=base_inputs["pixel_values"].dtype)

    logits = out.logits  # [B, T, V]
    ids_full = ti["input_ids"][0].tolist()
    target_ids = processor.tokenizer(target_text, add_special_tokens=False).input_ids
    if len(target_ids) == 0:
        return logits[:, -1, :].max(dim=-1).values.mean()

    m = len(target_ids)
    start = -1
    if ids_full[-m:] == target_ids:
        start = len(ids_full) - m
    else:
        for i in range(len(ids_full) - m, -1, -1):
            if ids_full[i : i + m] == target_ids:
                start = i
                break

    if start == -1:
        return logits[:, -1, :].max(dim=-1).values.mean()

    scores = []
    for j, tid in enumerate(target_ids):
        pos = start + j - 1
        if pos < 0 or pos >= logits.shape[1]:
            continue
        scores.append(logits[0, pos, tid])
    if not scores:
        return logits[:, -1, :].max(dim=-1).values.mean()
    return torch.stack(scores).mean()


@dataclass
class CamConfig:
    method: str = "score"  # 'score' or 'gscore'
    score_mode: str = "next_token"  # 'next_token' or 'teacher_forced'
    target_text: Optional[str] = None
    top_k: int = 64
    group_size: int = 8
    weight_norm: str = "softmax"  # 'softmax' or 'none'
    alpha: float = 0.5
    interpolation: str = "bilinear"


class ScoreCam:
    def __init__(
        self,
        model,
        processor: AutoProcessor,
        target_layer_name: str,
        target_layer: torch.nn.Module,
        device: torch.device,
        dtype: torch.dtype,
        cfg: CamConfig,
    ) -> None:
        self.model = model
        self.processor = processor
        self.layer_name = target_layer_name
        self.layer = target_layer
        self.device = device
        self.dtype = dtype
        self.cfg = cfg
        self._acts: Optional[torch.Tensor] = None
        self._fwd_handle = None
        self._last_prompt: Optional[str] = None
        self._last_image: Optional[Image.Image] = None

    def _install_hook(self) -> None:
        def fwd_hook(m, inp, out):
            self._acts = out if isinstance(out, torch.Tensor) else out[0]
            if isinstance(self._acts, torch.Tensor):
                self._acts.retain_grad()

        self._fwd_handle = self.layer.register_forward_hook(fwd_hook)

    def _remove_hook(self) -> None:
        if self._fwd_handle is not None:
            self._fwd_handle.remove()
            self._fwd_handle = None

    def _get_score_scalar(self, outputs, base_inputs: Dict[str, torch.Tensor]) -> torch.Tensor:
        if self.cfg.score_mode == "next_token":
            return score_next_token(outputs, self.processor, base_inputs, target_text=self.cfg.target_text)
        elif self.cfg.score_mode == "teacher_forced":
            if self._last_prompt is None or self._last_image is None:
                return score_next_token(outputs, self.processor, base_inputs, target_text=self.cfg.target_text)
            return score_teacher_forced(
                self.model,
                self.processor,
                self._last_prompt,
                self._last_image,
                base_inputs,
                target_text=self.cfg.target_text or "",
            )
        else:
            raise ValueError(f"Unknown score_mode: {self.cfg.score_mode}")

    def _reshape_acts_to_chw(self, acts: torch.Tensor) -> torch.Tensor:
        if acts.dim() == 4:  # [B,C,H,W] or [B,H,W,C]
            if acts.shape[1] <= 8 and acts.shape[-1] > 8:
                a = acts[0].permute(2, 0, 1).contiguous()
            else:
                a = acts[0]
            return a
        elif acts.dim() == 3:  # [B,N,D]
            a = acts[0]
            n, d = a.shape
            s = square_side_or_minus_one(n)
            if s == -1 and n > 1 and square_side_or_minus_one(n - 1) != -1:
                s = square_side_or_minus_one(n - 1)
                a = a[1:]
                n = a.shape[0]
            if s == -1:
                s_try = int(math.sqrt(n))
                s = s_try
                a = a[: s * s]
            a = a.transpose(0, 1).contiguous().view(d, s, s)
            return a
        elif acts.dim() == 2:  # [N,D]
            n, d = acts.shape
            s = square_side_or_minus_one(n)
            if s == -1 and n > 1 and square_side_or_minus_one(n - 1) != -1:
                s = square_side_or_minus_one(n - 1)
                acts = acts[1:]
            if s == -1:
                s = int(math.sqrt(n))
                acts = acts[: s * s]
            return acts.transpose(0, 1).contiguous().view(d, s, s)
        elif acts.dim() == 1:
            return acts.view(acts.shape[0], 1, 1)
        else:
            raise ValueError(f"Unsupported activation shape: {acts.shape}")

    def _build_masks(self, acts_chw: torch.Tensor, target_hw: Tuple[int, int]) -> torch.Tensor:
        c, h, w = acts_chw.shape
        norm = normalize_minmax(acts_chw)
        up = F.interpolate(
            norm.unsqueeze(0),
            size=target_hw,
            mode=self.cfg.interpolation,
            align_corners=False if self.cfg.interpolation != "nearest" else None,
        )
        return up.squeeze(0)

    @torch.no_grad()
    def _weights_from_forwards(self, base_inputs: Dict[str, torch.Tensor], masks: torch.Tensor) -> torch.Tensor:
        pv = base_inputs["pixel_values"]
        b, _, H, W = pv.shape
        c = masks.shape[0]
        weights = []
        for i in range(c):
            m = masks[i].clamp(0, 1).view(1, 1, H, W)
            pv_masked = pv * m
            masked_inputs = {k: (v if k != "pixel_values" else pv_masked) for k, v in base_inputs.items()}
            out = self.model(**masked_inputs, use_cache=False)
            s = self._get_score_scalar(out, masked_inputs)
            weights.append(s.detach())
            # Explicitly free per-iteration tensors to avoid fragmentation
            try:
                del out, pv_masked, masked_inputs
            except Exception:
                pass
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        w = torch.stack(weights)
        if self.cfg.weight_norm == "softmax":
            return torch.softmax(w, dim=0)
        return w / (w.abs().sum() + 1e-8)

    def _compute_grad_importance(self, score_scalar: torch.Tensor) -> Optional[torch.Tensor]:
        try:
            self.model.zero_grad(set_to_none=True)
            score_scalar.backward(retain_graph=True)
            if self._acts is not None and self._acts.grad is not None:
                g = self._acts.grad
                if g.dim() == 4:
                    imp = g.abs().mean(dim=(-2, -1))
                    return imp[0]
                elif g.dim() == 3:
                    imp = g.abs().mean(dim=1)
                    return imp[0]
                elif g.dim() == 2:
                    imp = g.abs().mean(dim=0)
                    return imp
                else:
                    return g.abs()
        except Exception:
            return None
        return None

    def run(self, image: Image.Image, prompt: str, output_dir: str) -> Dict:
        ensure_dir(output_dir)

        inputs = self.processor(text=prompt, images=image, return_tensors="pt")
        inputs = {k: (v.to(self.device, non_blocking=True)) for k, v in inputs.items()}
        for k in list(inputs.keys()):
            if isinstance(inputs[k], torch.Tensor) and inputs[k].is_floating_point():
                inputs[k] = inputs[k].to(self.dtype)

        self._last_prompt = prompt
        self._last_image = image

        self._install_hook()
        outputs = self.model(**inputs, use_cache=False)
        self._remove_hook()

        if self._acts is None:
            raise RuntimeError(f"Failed to capture activations at layer '{self.layer_name}'.")

        # gradient-guided selection score
        if self.cfg.method.lower() == "gscore" and self.cfg.score_mode == "teacher_forced":
            score_scalar_for_grad = score_next_token(outputs, self.processor, inputs, target_text=self.cfg.target_text)
        else:
            score_scalar_for_grad = self._get_score_scalar(outputs, inputs)

        acts_chw = self._reshape_acts_to_chw(self._acts)
        H, W = inputs["pixel_values"].shape[-2:]
        masks = self._build_masks(acts_chw, (H, W))

        if self.cfg.method.lower() == "gscore":
            grad_imp = self._compute_grad_importance(score_scalar_for_grad)
            if grad_imp is None:
                weights = self._weights_from_forwards(inputs, masks)
            else:
                c = masks.shape[0]
                k = min(self.cfg.top_k, c)
                vals, idx = torch.topk(grad_imp, k)
                groups: List[List[int]] = []
                for i in range(0, k, self.cfg.group_size):
                    groups.append(idx[i : min(i + self.cfg.group_size, k)].tolist())

                group_scores = []
                group_members = []
                for g in groups:
                    gm = masks[g].mean(dim=0, keepdim=True)
                    pv_masked = inputs["pixel_values"] * gm.view(1, 1, H, W)
                    masked_inputs = {k: (v if k != "pixel_values" else pv_masked) for k, v in inputs.items()}
                    with torch.no_grad():
                        out = self.model(**masked_inputs, use_cache=False)
                        s = self._get_score_scalar(out, masked_inputs)
                    group_scores.append(s.detach())
                    group_members.append(g)
                    # Free per-group tensors
                    try:
                        del out, pv_masked, masked_inputs, gm
                    except Exception:
                        pass
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

                weights = torch.zeros(masks.shape[0], device=inputs["pixel_values"].device, dtype=inputs["pixel_values"].dtype)
                for s, g in zip(group_scores, group_members):
                    gi = grad_imp[g].clamp_min(0)
                    if gi.sum() <= 0:
                        continue
                    wi = s * (gi / gi.sum())
                    weights[g] = wi
                if self.cfg.weight_norm == "softmax":
                    nonzero = weights != 0
                    if nonzero.any():
                        weights[nonzero] = torch.softmax(weights[nonzero], dim=0)
                else:
                    ssum = weights.abs().sum()
                    if ssum > 0:
                        weights = weights / ssum
        else:
            weights = self._weights_from_forwards(inputs, masks)

        cam = torch.einsum("c,chw->hw", weights, acts_chw)
        cam = F.relu(cam)
        cam = (cam - cam.min()) / (cam.max() - cam.min() + 1e-8)
        cam_up = F.interpolate(
            cam[None, None],
            size=(H, W),
            mode=self.cfg.interpolation,
            align_corners=False if self.cfg.interpolation != "nearest" else None,
        ).squeeze()

        base = inputs["pixel_values"][0]
        base_vis = base.clamp(0, 1)
        try:
            # attempt to unnormalize using underlying image_processor settings
            mean = torch.tensor(self.processor.image_processor.means[0], device=base.device, dtype=base.dtype).view(3, 1, 1)
            std = torch.tensor(self.processor.image_processor.stds[0], device=base.device, dtype=base.dtype).view(3, 1, 1)
            base_vis = (base[:3] * std + mean).clamp(0, 1)
        except Exception:
            pass

        overlay = overlay_heatmap(base_vis, cam_up, alpha=self.cfg.alpha)

        return {"cam": cam_up.detach().cpu().float().numpy(), "overlay": overlay, "weights": to_numpy(weights)}


def save_results(out: Dict, meta: Dict, out_dir: str) -> None:
    ensure_dir(out_dir)
    np.save(os.path.join(out_dir, "cam.npy"), out["cam"]) 
    from PIL import Image as PILImage
    PILImage.fromarray(out["overlay"]).save(os.path.join(out_dir, "overlay.png"))
    with open(os.path.join(out_dir, "meta.json"), "w", encoding="utf-8") as f:
        json.dump(meta, f, indent=2, ensure_ascii=False)


def main():
    parser = argparse.ArgumentParser(description="Score-CAM / gScore-CAM for OpenVLA")
    parser.add_argument("--model_path", type=str, default="/home/<USER>/X/models/OpenVLA")
    parser.add_argument("--image_path", type=str, required=True)
    # Either provide a full prompt or just an instruction string (we'll build the prompt like OpenVLA deploy script)
    parser.add_argument("--prompt", type=str, default=None)
    parser.add_argument("--instruction", type=str, default=None)
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/X/Info_flow/Info_flow_OpenVLA/X06Score-CAM/Result")
    parser.add_argument("--method", type=str, default="score", choices=["score", "gscore"])
    parser.add_argument("--score_mode", type=str, default="next_token", choices=["next_token", "teacher_forced"])
    parser.add_argument("--target_text", type=str, default=None)
    parser.add_argument("--target_layer", type=str, default=None)
    parser.add_argument("--top_k", type=int, default=64)
    parser.add_argument("--group_size", type=int, default=8)
    parser.add_argument("--alpha", type=float, default=0.5)
    parser.add_argument("--interpolation", type=str, default="bilinear", choices=["nearest", "bilinear", "bicubic"])
    parser.add_argument("--weight_norm", type=str, default="softmax", choices=["softmax", "none"])

    args = parser.parse_args()
    device, dtype = auto_device_dtype()

    # Load OpenVLA
    processor = AutoProcessor.from_pretrained(args.model_path, trust_remote_code=True)
    model = AutoModelForVision2Seq.from_pretrained(
        args.model_path,
        trust_remote_code=True,
        torch_dtype=dtype,
        low_cpu_mem_usage=True,
    ).to(device)
    model.eval()

    # Resolve target layer within OpenVLA vision backbone
    layer_name, layer = resolve_target_layer(model, args.target_layer)

    # Build prompt
    if args.prompt is not None:
        prompt = args.prompt
    elif args.instruction is not None:
        prompt = get_openvla_prompt(args.instruction, args.model_path)
    else:
        raise ValueError("Provide either --prompt or --instruction to construct the model input text.")

    cfg = CamConfig(
        method=args.method,
        score_mode=args.score_mode,
        target_text=args.target_text,
        top_k=args.top_k,
        group_size=args.group_size,
        weight_norm=args.weight_norm,
        alpha=args.alpha,
        interpolation=args.interpolation,
    )

    runner = ScoreCam(model, processor, layer_name, layer, device, dtype, cfg)
    image = Image.open(args.image_path).convert("RGB")
    out = runner.run(image=image, prompt=prompt, output_dir=args.output_dir)

    meta = {
        "model": args.model_path,
        "prompt": prompt,
        "method": args.method,
        "score_mode": args.score_mode,
        "target_text": args.target_text,
        "target_layer": layer_name,
        "dtype": str(dtype),
        "device": str(device),
        "alpha": args.alpha,
        "interpolation": args.interpolation,
        "top_k": args.top_k,
        "group_size": args.group_size,
    }

    save_results(out, meta, args.output_dir)
    print(f"Saved CAM to: {args.output_dir}")


if __name__ == "__main__":
    main()
