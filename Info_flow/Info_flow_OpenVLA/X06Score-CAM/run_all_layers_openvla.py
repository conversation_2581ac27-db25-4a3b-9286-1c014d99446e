#!/usr/bin/env python3
import argparse
import os
import re
from typing import Dict, List, Tuple

import torch
from PIL import Image
from transformers import AutoModelForVision2Seq, AutoProcessor


def load_scorecam_module():
    import importlib.util
    here = os.path.dirname(__file__)
    path = os.path.join(here, 'run_score_cam_openvla.py')
    spec = importlib.util.spec_from_file_location('score_cam_internal_openvla', path)
    mod = importlib.util.module_from_spec(spec)
    assert spec.loader is not None
    spec.loader.exec_module(mod)
    return mod


def sanitize(name: str) -> str:
    return re.sub(r'[^a-zA-Z0-9_\-]', '_', name)


def list_vision_layers(model: torch.nn.Module, include_mlp: bool = False, include_patch: bool = True) -> List[str]:
    names: List[str] = []
    # Prefer ViT blocks inside the HF Prismatic vision backbone (featurizer and fused_featurizer)
    for name, _ in model.named_modules():
        # Timm ViT attention blocks end with '.attn'
        if name.startswith('vision_backbone.featurizer.blocks') and name.endswith('attn'):
            names.append(name)
        if name.startswith('vision_backbone.fused_featurizer.blocks') and name.endswith('attn'):
            names.append(name)
        if include_mlp:
            if name.startswith('vision_backbone.featurizer.blocks') and name.endswith('mlp'):
                names.append(name)
            if name.startswith('vision_backbone.fused_featurizer.blocks') and name.endswith('mlp'):
                names.append(name)

    def key(n: str) -> Tuple[int, int, str]:
        # Sort by (backbone order, layer index, name)
        bb = 0 if n.startswith('vision_backbone.featurizer.') else 1
        m = re.search(r'blocks\.(\d+)\.', n)
        idx = int(m.group(1)) if m else -1
        return (bb, idx, n)

    names.sort(key=key)

    if include_patch:
        # Add patch embedding projections (both backbones if present)
        for pe in [
            'vision_backbone.featurizer.patch_embed.proj',
            'vision_backbone.fused_featurizer.patch_embed.proj',
        ]:
            if any(name == pe for name, _ in model.named_modules()):
                names = [pe] + names
    return names


def main():
    mod = load_scorecam_module()

    parser = argparse.ArgumentParser(description='Run Score-CAM/gScore-CAM across OpenVLA vision layers')
    parser.add_argument('--model_path', type=str, default='/home/<USER>/X/models/OpenVLA')
    parser.add_argument('--image_path', type=str, required=True)
    # Either a complete prompt or instruction to build OpenVLA-style prompt
    parser.add_argument('--prompt', type=str, default=None)
    parser.add_argument('--instruction', type=str, default=None)
    parser.add_argument('--output_dir', type=str, required=True)
    parser.add_argument('--method', type=str, default='gscore', choices=['score', 'gscore'])
    parser.add_argument('--score_mode', type=str, default='next_token', choices=['next_token', 'teacher_forced'])
    parser.add_argument('--target_text', type=str, default=None)
    parser.add_argument('--top_k', type=int, default=64)
    parser.add_argument('--group_size', type=int, default=8)
    parser.add_argument('--weight_norm', type=str, default='none', choices=['softmax', 'none'])
    parser.add_argument('--alpha', type=float, default=0.5)
    parser.add_argument('--interpolation', type=str, default='bilinear', choices=['nearest', 'bilinear', 'bicubic'])
    parser.add_argument('--include_mlp', action='store_true', help='Also run on MLP blocks')
    parser.add_argument('--no_patch', action='store_true', help='Exclude patch embedding conv')
    parser.add_argument('--max_layers', type=int, default=0, help='Limit number of layers (0 = all)')
    parser.add_argument('--backbone', type=str, default='all', choices=['all','featurizer','fused'], help='Limit to a specific vision backbone')
    parser.add_argument('--start_block', type=int, default=-1, help='Start from this block index for blocks.* layers')
    parser.add_argument('--stop_block', type=int, default=10000, help='Stop after this block index (inclusive)')

    args = parser.parse_args()

    device, dtype = mod.auto_device_dtype()

    processor = AutoProcessor.from_pretrained(args.model_path, trust_remote_code=True)
    model = AutoModelForVision2Seq.from_pretrained(
        args.model_path,
        trust_remote_code=True,
        torch_dtype=dtype,
        low_cpu_mem_usage=True,
    )
    model.to(device).eval()

    all_layers = list_vision_layers(model, include_mlp=args.include_mlp, include_patch=(not args.no_patch))
    if args.backbone != 'all':
        key = 'fused_featurizer' if args.backbone == 'fused' else 'featurizer'
        all_layers = [n for n in all_layers if f"vision_backbone.{key}." in n]
    if not all_layers:
        raise RuntimeError('No vision layers found to run Score-CAM on.')

    # Optional slice by block index for resume/partial runs
    if args.start_block > -1 or args.stop_block < 10000:
        sel = []
        for n in all_layers:
            m = re.search(r'blocks\.(\d+)\.', n)
            if m:
                idx = int(m.group(1))
                if idx < args.start_block or idx > args.stop_block:
                    continue
            sel.append(n)
        all_layers = sel

    if args.max_layers and args.max_layers > 0:
        all_layers = all_layers[: args.max_layers]

    name2mod: Dict[str, torch.nn.Module] = {name: m for name, m in model.named_modules()}
    os.makedirs(args.output_dir, exist_ok=True)
    image = Image.open(args.image_path).convert('RGB')

    # Build prompt
    if args.prompt is not None:
        prompt = args.prompt
    elif args.instruction is not None:
        prompt = mod.get_openvla_prompt(args.instruction, args.model_path)
    else:
        raise ValueError('Provide either --prompt or --instruction.')

    for lname in all_layers:
        if lname not in name2mod:
            print(f"[Skip] Layer not found: {lname}")
            continue
        layer = name2mod[lname]
        cfg = mod.CamConfig(
            method=args.method,
            score_mode=args.score_mode,
            target_text=args.target_text,
            top_k=args.top_k,
            group_size=args.group_size,
            weight_norm=args.weight_norm,
            alpha=args.alpha,
            interpolation=args.interpolation,
        )
        runner = mod.ScoreCam(model, processor, lname, layer, device, dtype, cfg)
        out = runner.run(image=image, prompt=prompt, output_dir=args.output_dir)

        out_dir = os.path.join(args.output_dir, sanitize(lname))
        os.makedirs(out_dir, exist_ok=True)
        meta = {
            'model': args.model_path,
            'prompt': prompt,
            'method': args.method,
            'score_mode': args.score_mode,
            'target_text': args.target_text,
            'target_layer': lname,
            'dtype': str(dtype),
            'device': str(device),
            'alpha': args.alpha,
            'interpolation': args.interpolation,
            'top_k': args.top_k,
            'group_size': args.group_size,
        }
        mod.save_results(out, meta, out_dir)
        print(f"Saved: {out_dir}")

        # Cleanup per-layer GPU memory
        try:
            del runner
            import gc
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except Exception:
            pass


if __name__ == '__main__':
    main()
