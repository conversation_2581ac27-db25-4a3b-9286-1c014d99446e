<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <!-- ===== Meta tags (your "business card") ===== -->
  <meta name="description" content="Manipulation Facing Threats">
  <meta property="og:title" content="Manipulation Facing Threats"/>
  <meta property="og:description"
        content="Evaluating Physical Vulnerabilities in End-to-End Vision Language Action Models"/>
  <meta property="og:url" content="https://github.com/Researchtopic/Manipulation-Facing-Threats"/>

  <meta name="twitter:title" content="Manipulation Facing Threats">
  <meta name="twitter:description"
        content="A comprehensive evaluation of physical vulnerabilities in Vision Language Action Models">

  <title>Manipulation Facing Threats</title>
  <link rel="icon" type="image/png" href="static/images/favicon.ico">
  <link href="https://fonts.googleapis.com/css?family=Google+Sans|Noto+Sans|Castoro" rel="stylesheet">

  <!-- ===== Styles ===== -->
  <link rel="stylesheet" href="static/css/bulma.min.css">
  <link rel="stylesheet" href="static/css/bulma-carousel.min.css">
  <link rel="stylesheet" href="static/css/bulma-slider.min.css">
  <link rel="stylesheet" href="static/css/fontawesome.all.min.css">
  <link rel="stylesheet"
        href="https://cdn.jsdelivr.net/gh/jpswalsh/academicons@1/css/academicons.min.css">
  <link rel="stylesheet" href="static/css/index.css">

  <!-- ===== Scripts ===== -->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script defer src="static/js/fontawesome.all.min.js"></script>
  <script src="static/js/bulma-carousel.min.js"></script>
  <script src="static/js/bulma-slider.min.js"></script>
  <script src="static/js/index.js"></script>

<style>
  .framework-img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
  }

  .results-table {
    width: 100%;
    margin: 20px auto;
    border-collapse: collapse;
  }

  .results-table th,
  .results-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid #ddd;
  }

  .results-table tr:nth-child(even) {
    background-color: #f2f2f2;
  }

  .results-table th {
    background-color: #4CAF50;
    color: white;
  }

  .demo-video {
    max-width: 100%;
    height: auto;
    margin: 10px 0;
  }

  .demo-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
  }

  .demo-table td {
    text-align: center;
    padding: 10px;
    border: 1px solid #ddd;
  }

  .demo-table img,
  .demo-table video {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
  }
</style>

</head>

<body>
<!-- ============ NAV BAR ============ -->
<nav class="navbar" role="navigation" aria-label="main navigation">
  <div class="navbar-brand">
    <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false">
      <span aria-hidden="true"></span><span aria-hidden="true"></span><span aria-hidden="true"></span>
    </a>
  </div>
</nav>

<!-- ============ HERO TITLE ============ -->
<section class="hero">
  <div class="hero-body">
    <div class="container is-max-desktop">
      <div class="columns is-centered">
        <div class="column has-text-centered">
          <h1 class="title is-1 publication-title">
            Manipulation Facing Threats:<br>
            <span style="font-size:0.8em;">
              Evaluating Physical Vulnerabilities in End-to-End Vision Language Action Models
            </span>
          </h1>

<div class="is-size-5 publication-authors has-text-centered" style="margin-top: 1rem;">
  <span class="author-block">Hao Cheng<sup style="color:#ed4b82;">1✶</sup>,</span>
  <span class="author-block">Erjia Xiao<sup style="color:#ed4b82;">1✶</sup>,</span>
  <span class="author-block">Chengyuan Yu<sup style="color:#f39c12;">3</sup>,</span>
  <span class="author-block">Yao Zhao<sup style="color:#9b59b6;">4</sup>,</span>
  <span class="author-block">Jiahang Cao<sup style="color:#ed4b82;">1</sup>,</span>
</div>
<div class="is-size-5 publication-authors has-text-centered">
  <span class="author-block">Qiang Zhang<sup style="color:#ed4b82;">1</sup>,</span>
  <span class="author-block">Jiaxu Wang<sup style="color:#ed4b82;">1</sup>,</span>
  <span class="author-block">Mengshu Sun<sup style="color:#3498db;">6</sup>,</span>
  <span class="author-block">Kaidi Xu<sup style="color:#6fbf73;">5</sup>,</span>
  <span class="author-block">Jindong Gu<sup style="color:#6fbf73;">2</sup>,</span>
  <span class="author-block">Renjing Xu<sup style="color:#ed4b82;">1†</sup></span>
</div>

<div class="is-size-5 publication-authors has-text-centered" style="margin-bottom: 2rem;">
  <span class="author-block"><sup style="color:#ed4b82;">1</sup>The Hong Kong University of Science and Technology (Guangzhou),</span>
  <span class="author-block"><sup style="color:#6fbf73;">2</sup>University of Oxford,</span>
  <span class="author-block"><sup style="color:#f39c12;">3</sup>Hohai University,</span>
  <span class="author-block"><sup style="color:#9b59b6;">4</sup>Hunan University,</span>
  <span class="author-block"><sup style="color:#6fbf73;">5</sup>Drexel University,</span>
  <span class="author-block"><sup style="color:#3498db;">6</sup>Beijing University of Technology</span>
</div>

<div class="is-size-6 publication-authors has-text-centered" style="margin-bottom: 1rem;">
  <span class="author-block">✶ indicates equal contribution, † Correspondence authors</span>
</div>

<div class="buttons-container">
  <span class="link-block">
    <a href="https://arxiv.org/abs/2409.13174" target="_blank"
       class="external-link button is-normal is-rounded is-dark">
        <span class="icon"><i class="ai ai-arxiv"></i></span>
        <span>arXiv</span>
    </a>
  </span>
  
  <span class="link-block">
    <a href="https://www.youtube.com/watch?v=Zd7a2e9cEMY" target="_blank"
       class="external-link button is-normal is-rounded is-dark">
      <span class="icon"><i class="fab fa-youtube"></i></span>
      <span>Video</span>
    </a>
  </span>
</div> </div>
      </div>
    </div>
  </div>
</section>

<!-- ============ ABSTRACT ============ -->
<section class="section hero is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered has-text-centered">
      <div class="column is-four-fifths">
        <h2 class="title is-3">📖 Abstract</h2>
        <div class="content has-text-justified">
          <p>
            Recently, driven by advancements in Multimodal Large Language Models (MLLMs), Vision Language Action Models (VLAMs) are being proposed to achieve better performance in open-vocabulary scenarios for robotic manipulation tasks. Since manipulation tasks involve direct interaction with the physical world, ensuring robustness and safety during the execution of this task is always a very critical issue. In this paper, by synthesizing current safety research on MLLMs and the specific application scenarios of the manipulation task in the physical world, we comprehensively evaluate VLAMs in the face of potential physical threats. Specifically, we propose the Physical Vulnerability Evaluating Pipeline (PVEP) that can incorporate as many visual modal physical threats as possible for evaluating the physical robustness of VLAMs. The physical threats in PVEP specifically include Out-of-Distribution, Typography-based Visual Prompt, and Adversarial Patch Attacks. By comparing the performance fluctuations of VLAMs before and after being attacked, we provide generalizable <b><i>Analyses</i></b> of how VLAMs respond to different physical security threats.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- ============ FRAMEWORK ============ -->
<section class="section hero is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered has-text-centered">
      <div class="column is-four-fifths">
        <h2 class="title is-3">🔧 Framework</h2>
        <div class="content has-text-justified">
          <p>
            <b>Overview of Framework.</b> The figure below illustrates the overall framework for evaluating physical security threats to VLAMs using the Physical Vulnerability Evaluating Pipeline (PVEP).
          </p>
          <img src="./figures/consolidated_v3_00.png" alt="Framework Overview" class="framework-img">
        </div>
      </div>
    </div>
  </div>
</section><!-- ============ EXPERIMENT RESULTS ============ -->
<section class="section hero is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered has-text-centered">
      <div class="column is-four-fifths">
        <h2 class="title is-3">🚀 Experiment Results</h2>
        
        <!-- LLaRA Results -->
        <div class="content has-text-justified">
          <h3 class="title is-4">LLaRA Results</h3>
          <div style="width: 80%; margin: 0 auto;">
            <img src="./figures/table1.png" alt="LLaRA Table Results" style="width: 75%; display: block; margin: 0 auto;">
            <img src="./figures/llarastep_00.png" alt="LLaRA Step Results" style="width: 75%; display: block; margin: 0 auto;">
          </div>
          <p>
            <b>LLaRA Results:</b> Under 3 physical attack categories: (left) Time steps (with a maximum limit of 8) of LLaRA on 14 VIMA tasks that are listed in TABLE I. (right) Failure rates of the OOD attacks with other levels that are not listed in TABLE I.
          </p>
        </div>

        <!-- OpenVLA Results -->
        <div class="content has-text-justified">
          <h3 class="title is-4">OpenVLA Results</h3>
          <div style="width: 99%; margin: 0 auto;">
            <img src="./figures/table2.png" alt="OpenVLA Table Results" style="width: 75%; display: block; margin: 0 auto;">
            <img src="./figures/openvlastep_00.png" alt="OpenVLA Step Results" style="width: 75%; display: block; margin: 0 auto;">
          </div>
          <p>
            <b>OpenVLA Results:</b> Under 3 physical attack categories: (left) Time steps (with a maximum limit of 300) of OpenVLA on 6 SimplerEnv tasks that are listed in TABLE II. (right) Failure rates of the OOD attacks with other levels that are not listed in TABLE II.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- ============ DEMO ============ -->
<section class="section hero is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered has-text-centered">
      <div class="column is-four-fifths">
        <h2 class="title is-3">🎬 Demo</h2>
        
        <!-- LLaRA Manipulation -->
        <div class="content has-text-justified">
          <h3 class="title is-4 has-text-centered">LLaRA Manipulation</h3>
          <table class="demo-table">
            <tbody>
              <tr>
                <td>
                  <img src="./videos/LLaRA/blur_doutub_gif.gif" alt="LLaRA Blur Attack">
                </td>
                <td>
                  <img src="./videos/LLaRA/gaussian_doutub_gif.gif" alt="LLaRA Gaussian Attack">
                </td>
                <td>
                  <img src="./videos/LLaRA/bright_doutub_gif.gif" alt="LLaRA Bright Attack">
                </td>
              </tr>
              <tr>
                <td>
                  <img src="./videos/LLaRA/dark_doutub_gif.gif" alt="LLaRA Dark Attack">
                </td>
                <td>
                  <img src="./videos/LLaRA/typo_doutub_gif.gif" alt="LLaRA Typography Attack">
                </td>
                <td>
                  <img src="./videos/LLaRA/advpatch_doutub_gif.gif" alt="LLaRA Adversarial Patch Attack">
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- OpenVLA Manipulation -->
        <div class="content has-text-justified">
          <h3 class="title is-4 has-text-centered">OpenVLA Manipulation</h3>
          <table class="demo-table">
            <tbody>
              <tr>
                <td>
                  <video controls autoplay muted loop class="demo-video">
                    <source src="./videos/openvla/openvla_blur.mp4" type="video/mp4">
                  </video>
                </td>
                <td>
                  <video controls autoplay muted loop class="demo-video">
                    <source src="./videos/openvla/openvla_gn.mp4" type="video/mp4">
                  </video>
                </td>
                <td>
                  <video controls autoplay muted loop class="demo-video">
                    <source src="./videos/openvla/openvla_bright.mp4" type="video/mp4">
                  </video>
                </td>
              </tr>
              <tr>
                <td>
                  <video controls autoplay muted loop class="demo-video">
                    <source src="./videos/openvla/openvla_dark.mp4" type="video/mp4">
                  </video>
                </td>
                <td>
                  <video controls autoplay muted loop class="demo-video">
                    <source src="./videos/openvla/openvla_typo.mp4" type="video/mp4">
                  </video>
                </td>
                <td>
                  <video controls autoplay muted loop class="demo-video">
                    <source src="./videos/openvla/openvla_advpatch.mp4" type="video/mp4">
                  </video>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="content has-text-justified" style="margin-top: 20px;">
            <p>
              These videos are the attacked demos of LLaRA and OpenVLA. The attacking types are blurring, gaussian noise, brighter, darker, visual prompt, and adversarial patch.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- ============ BIBTEX ============ -->
<section class="section hero is-light">
  <div class="container is-max-desktop">
    <div class="columns is-centered has-text-centered">
      <div class="column is-four-fifths">
        <h2 class="title is-3">📚 BibTeX</h2>
        <div class="content has-text-left">
          <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"><code>@article{cheng2024manipulation,
  title={Manipulation Facing Threats: Evaluating Physical Vulnerabilities in End-to-End Vision Language Action Models},
  author={Cheng, Hao and Xiao, Erjia and Yu, Chengyuan and Yao, Zhao and Cao, Jiahang and Zhang, Qiang and Wang, Jiaxu and Sun, Mengshu and Xu, Kaidi and Gu, Jindong and others},
  journal={arXiv preprint arXiv:2409.13174},
  year={2024}
}</code></pre>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- ============ FOOTER ============ -->
<footer class="footer">
  <div class="container">
    <div class="columns is-centered">
      <div class="column is-8">
        <div class="content">
          <p>
            This website is licensed under a <a rel="license"
            href="http://creativecommons.org/licenses/by-sa/4.0/">Creative
            Commons Attribution-ShareAlike 4.0 International License</a>.
          </p>
          <p>
            Template borrowed from <a href="https://github.com/nerfies/nerfies.github.io">Nerfies</a>.
          </p>
        </div>
      </div>
    </div>
  </div>
</footer>

</body>
</html>