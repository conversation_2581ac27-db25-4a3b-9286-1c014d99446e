# Manipulation Facing Threats: Enhanced Project Page

This project page combines the visual design of AudioBench with the research content of Manipulation Facing Threats.

## Overview

**Manipulation Facing Threats: Evaluating Physical Vulnerabilities in End-to-End Vision Language Action Models**

This research comprehensively evaluates Vision Language Action Models (VLAMs) in the face of potential physical threats, proposing the Physical Vulnerability Evaluating Pipeline (PVEP) to assess physical robustness.

## Features

- **Modern Design**: Utilizes Bulma CSS framework for responsive, professional layout
- **Research Content**: Complete presentation of manipulation threat evaluation research
- **Interactive Elements**: Clean, academic-style presentation with proper typography
- **Media Integration**: Seamless display of experimental results, tables, and demo videos

## Structure

```
Manipulation-Threats-Enhanced/
├── index.html          # Main project page
├── static/             # Static assets
│   ├── css/           # Stylesheets (from AudioBench)
│   ├── js/            # JavaScript files
│   └── images/        # Image assets
├── figures/           # Research figures and tables
├── videos/            # Demo videos
└── README.md          # This file
```

## Key Research Contributions

1. **Physical Vulnerability Evaluating Pipeline (PVEP)** - Comprehensive evaluation framework
2. **Multi-threat Assessment** - Out-of-Distribution, Typography-based Visual Prompt, and Adversarial Patch Attacks
3. **Model Comparisons** - Detailed analysis of LLaRA and OpenVLA performance under various attack scenarios

## Citation

```bibtex
@article{cheng2024manipulation,
  title={Manipulation Facing Threats: Evaluating Physical Vulnerabilities in End-to-End Vision Language Action Models},
  author={Cheng, Hao and Xiao, Erjia and Yu, Chengyuan and Yao, Zhao and Cao, Jiahang and Zhang, Qiang and Wang, Jiaxu and Sun, Mengshu and Xu, Kaidi and Gu, Jindong and others},
  journal={arXiv preprint arXiv:2409.13174},
  year={2024}
}
```

## License

This website is licensed under a [Creative Commons Attribution-ShareAlike 4.0 International License](http://creativecommons.org/licenses/by-sa/4.0/).