# @package _global_

defaults:
  - _self_
  - data: default
  - policy: bc_transformer_policy
  - train: default
  - eval: default
  - lifelong: base
  - test: null

seed: 10000
use_wandb: false
wandb_project: "lifelong learning"
folder: null # use default path
bddl_folder: null # use default path
init_states_folder: null # use default path
load_previous_model: false
device: "cuda"
task_embedding_format: "bert"
task_embedding_one_hot_offset: 1
pretrain: false
pretrain_model_path: ""
benchmark_name: "LIBERO_SPATIAL"
