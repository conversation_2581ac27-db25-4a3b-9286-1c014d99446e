<mujoco model="plant">
  <asset>
  <texture file="plant_texture.png" name="tex-plant" type="2d" />
  <material name="plant" reflectance="0.5" texrepeat="1 1" texture="tex-plant" texuniform="false" />
  <mesh file="visual/plant_vis.msh" name="plant_vis" scale="1.0 1.0 1.0" /><mesh file="collision/plant_ch.stl" name="plant_coll" scale="1.0 1.0 1.0" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="plant_vis" conaffinity="0" contype="0" group="1" material="plant" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="plant_coll" group="0" rgba="0.8 0.8 0.8 0.0" /></body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>