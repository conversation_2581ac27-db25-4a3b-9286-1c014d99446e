<mujoco model="textured">
  <asset>
  <texture file="texture_map.png" name="tex-textured" type="2d" />
  <material name="textured" reflectance="0.5" texrepeat="1 1" texture="tex-textured" texuniform="false" />
  <mesh file="visual/textured_vis.msh" name="textured_vis" scale="0.01 0.01 0.01" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="textured_vis" conaffinity="0" contype="0" group="1" material="textured" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.00050 0.00000 0.00063" quat="0.70711 0.00000 0.70711 0.00000" size="0.01371 0.02316 0.04010" group="0" rgba="0.8 0.8 0.8 0.3" /></body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>